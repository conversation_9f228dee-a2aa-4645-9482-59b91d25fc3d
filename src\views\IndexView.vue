<template>
    <div class="w-full h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        <!-- 顶部系统标题（如果需要可拆分组件，这里直接写） -->
        <div
            class="flex items-center justify-center py-2 bg-gradient-to-r from-blue-800 to-blue-900 text-white text-xl font-bold">
            漳平市卫生监督管理系统
        </div>
        <!-- 功能入口卡片区域 -->
        <div class="flex flex-wrap justify-center items-center px-4 py-8">
            <div class="w-64 h-40 bg-white rounded shadow mx-4 my-2 flex flex-col items-center justify-center cursor-pointer hover:shadow-lg transition-shadow"
                @click="handleClick('public-place')">
                <div
                    class="w-12 h-12 mb-2 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    公</div>
                <span class="text-lg font-semibold">公共场所在线监测</span>
            </div>
            <div class="w-64 h-40 bg-white rounded shadow mx-4 my-2 flex flex-col items-center justify-center cursor-pointer hover:shadow-lg transition-shadow"
                @click="handleClick('radiation-health')">
                <div
                    class="w-12 h-12 mb-2 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                    放</div>
                <span class="text-lg font-semibold">放射卫生在线监测</span>
            </div>
            <div class="w-64 h-40 bg-white rounded shadow mx-4 my-2 flex flex-col items-center justify-center cursor-pointer hover:shadow-lg transition-shadow"
                @click="handleClick('occupational-hazard')">
                <div
                    class="w-12 h-12 mb-2 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                    职</div>
                <span class="text-lg font-semibold">职业病危害因素在线监测</span>
            </div>
            <div class="w-64 h-40 bg-white rounded shadow mx-4 my-2 flex flex-col items-center justify-center cursor-pointer hover:shadow-lg transition-shadow"
                @click="handleClick('real-time-video')">
                <div
                    class="w-12 h-12 mb-2 bg-red-500 rounded-full flex items-center justify-center text-white font-bold">
                    视</div>
                <span class="text-lg font-semibold">实时视频监控</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()

const handleClick = (type) => {
    // 根据类型跳转不同路由，这里先写死示例，实际按项目路由配置调整
    switch (type) {
        case 'public-place':
            router.push('/public-place-monitor')
            break
        case 'radiation-health':
            router.push('/radiation-health-monitor')
            break
        case 'occupational-hazard':
            router.push('/occupational-hazard-monitor')
            break
        case 'real-time-video':
            router.push('/real-time-video-monitor')
            break
        default:
            break
    }
}
</script>

<style scoped>
/* 若需要覆盖或补充 Tailwind 样式可在这里写 */
</style>